import { zodResolver } from '@hookform/resolvers/zod';
import { OccupationCategory } from 'api/core/generated';
import {
  FormFieldNames,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { useRootContext } from 'context/root';
import {
  useEffectOnce,
  useGetCurrentApplication,
  useGetOccupationCategoriesDefault,
  useRudderStack,
  useUpdateUserInfoExtra,
} from 'hooks';
import { useDeleteIncomesLiabilitiesDocument } from 'hooks/use-delete-incomes-liabilities-document';
import { useUserIncomeLiabilityDocuments } from 'hooks/use-get-user-income-liability-documents';
import { useUploadUserIncomeLiabilitiesGroupDocument } from 'hooks/use-upload-user-income-iability-document';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { formatApplicationToContactExtraPageDataFormat } from 'services';
import {
  defaultUploadMaxSize,
  getOccupationCategoriesOptions,
} from 'services/occupation-service';
import { processGqlFormValidationErrors } from 'utils';
import * as z from 'zod';

const OccupationFormSchema = z.object({
  [FormFieldNames.occupationCategory]: z.nativeEnum(OccupationCategory),
});

export type OccupationFormType = z.infer<typeof OccupationFormSchema>;

export const useIncomeVerificationPageLogic = () => {
  const { t } = useTranslation(LocizeNamespaces.incomeVerification);
  const { t: tr } = useTranslation(LocizeNamespaces.errors);
  const { ruderStackEvents } = useRudderStack();
  const [documentIdToDelete, setDocumentIdToDelete] = useState<number | null>(
    null,
  );

  const [isUploading, setIsUploading] = useState(false);

  const {
    user,
    quietUserRefetch,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
  } = useRootContext();

  const {
    uploadUserIncomeLiabilitiesGroupDocument,
    uploadUserIncomeLiabilitiesGroupDocumentProcessing,
  } = useUploadUserIncomeLiabilitiesGroupDocument();

  const {
    userIncomeLiabilityDocumentsLoading,
    refetchUserIncomeLiabilityDocuments,
    getIncomeLiabilityDocuments,
    userIncomeLiabilityDocuments,
  } = useUserIncomeLiabilityDocuments();

  const userId = user?.id ?? null;

  const { getApplication, application, applicationLoading } =
    useGetCurrentApplication({
      onCompleted: (data) => {
        if (!userId) {
          throw new Error('User not found during documents request');
        }

        if (!data?.application?.id) {
          throw new Error('Application not found during documents request');
        }

        getIncomeLiabilityDocuments({
          application_id: data.application?.id,
          user_id: userId,
        });
      },
    });

  const {
    deleteIncomesLiabilitiesDocument,
    deleteIncomesLiabilitiesDocumentLoading,
  } = useDeleteIncomesLiabilitiesDocument();

  const { occupationCategories, occupationCategoriesLoading } =
    useGetOccupationCategoriesDefault({});
  const { updateUserInfoExtra } = useUpdateUserInfoExtra();

  const {
    ultimateBeneficialOwner,
    futureReducedEarnings,
    planningNewDebts,
    employmentDate,
    numberOfDependents,
    monthlyLivingExpenses,
    expenditureMonthly,
    netIncomeMonthly,
    occupationCategory,
    applicationUserInfoId,
    overdueDebt,
  } = formatApplicationToContactExtraPageDataFormat({
    ...application,
  });

  const form = useForm<OccupationFormType>({
    resolver: zodResolver(OccupationFormSchema),
    mode: 'onChange',
    defaultValues: {
      [FormFieldNames.occupationCategory]: occupationCategory ?? undefined,
    },
  });

  useEffectOnce(() => {
    getApplication();
  });

  useEffect(() => {
    if (occupationCategory && occupationCategories) {
      form.setValue(FormFieldNames.occupationCategory, occupationCategory);
    }
  }, [occupationCategory, form, occupationCategories]);

  const occupationCategoryOptions = getOccupationCategoriesOptions({
    t,
    occupationCategories,
  });

  const incomeVerificationPageDataLoading =
    applicationLoading || occupationCategoriesLoading;

  const onOccupationFormSubmit = async ({
    occupation_category,
  }: OccupationFormType) => {
    try {
      await updateUserInfoExtra({
        application_user_info_id: applicationUserInfoId ?? 0,
        number_of_dependents: numberOfDependents,
        expenditure_monthly: expenditureMonthly,
        monthly_living_expenses: monthlyLivingExpenses,
        net_income_monthly: netIncomeMonthly,
        planning_new_debts: planningNewDebts || 0,
        employment_date: employmentDate,
        future_reduced_earnings: futureReducedEarnings || 0,
        ultimate_beneficial_owner: ultimateBeneficialOwner,
        occupation_category,
        overdue_debt: overdueDebt,
        extra_income: 0,
      });

      await quietUserRefetch();

      await getPageUrlAndNavigate(true);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  const onDocumentsUploaded = async (files: File[]) => {
    if (!userId) {
      throw new Error('User not found during document upload');
    }

    if (!application?.id) {
      throw new Error('Application not found during document upload');
    }
    const maxFileSize = defaultUploadMaxSize * 1024 * 1024;
    try {
      setIsUploading(true);

      const oversizedFile = files.find((file) => file.size > maxFileSize);

      if (oversizedFile) {
        // request will fail
        toast.error(
          tr(LOCIZE_ERRORS_TRANSLATION_KEYS.uploadFileMaxSize, {
            max_file_size: defaultUploadMaxSize,
          }) as string,
        );
        return;
      }

      await uploadUserIncomeLiabilitiesGroupDocument({
        user_id: userId,
        application_id: application.id,
        documents: files,
      });

      await refetchUserIncomeLiabilityDocuments();
    } finally {
      setIsUploading(false);
    }
  };

  const onDocumentRemoved = async (id: number) => {
    try {
      setDocumentIdToDelete(id);

      const { data } = await deleteIncomesLiabilitiesDocument({
        user_income_liability_document_id: id,
      });

      if (data?.success) {
        await refetchUserIncomeLiabilityDocuments();
        setDocumentIdToDelete(null);
      }
    } catch {
      setDocumentIdToDelete(null);
      toast.error(tr(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError) as string);
    }
  };

  return useMemo(
    () => ({
      onOccupationFormSubmit,
      onDocumentsUploaded,
      onDocumentRemoved,
      occupationCategoryOptions,
      form,
      userIncomeLiabilityDocuments,
      userIncomeLiabilityDocumentsLoading,
      uploadUserIncomeLiabilitiesGroupDocumentProcessing,
      deleteIncomesLiabilitiesDocumentLoading,
      documentIdToDelete,
      pageUrlAndNavigationProcessing,
      getPageUrlAndNavigate,
      isUploading,
      incomeVerificationPageDataLoading,
    }),
    [
      onOccupationFormSubmit,
      occupationCategoryOptions,
      onDocumentsUploaded,
      onDocumentRemoved,
      form,
      userIncomeLiabilityDocuments,
      userIncomeLiabilityDocumentsLoading,
      uploadUserIncomeLiabilitiesGroupDocumentProcessing,
      deleteIncomesLiabilitiesDocumentLoading,
      documentIdToDelete,
      pageUrlAndNavigationProcessing,
      getPageUrlAndNavigate,
      incomeVerificationPageDataLoading,
      isUploading,
    ],
  );
};
