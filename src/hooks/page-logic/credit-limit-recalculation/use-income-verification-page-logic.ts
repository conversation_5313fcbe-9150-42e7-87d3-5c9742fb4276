import { zodResolver } from '@hookform/resolvers/zod';
import { OccupationCategory } from 'api/core/generated';
import {
  FormFieldNames,
  LOCIZE_ERRORS_TRANSLATION_KEYS,
  LocizeNamespaces,
} from 'app-constants';
import { useRootContext } from 'context/root';
import {
  useGetCreditLimitRecalculation,
  useGetOccupationCategoriesDefault,
  useRudderStack,
  useUpdateUserInfoExtra,
} from 'hooks';
import { useDeleteIncomesLiabilitiesDocument } from 'hooks/use-delete-incomes-liabilities-document';
import { useUserIncomeLiabilityDocuments } from 'hooks/use-get-user-income-liability-documents';
import { useUploadUserIncomeLiabilitiesGroupDocument } from 'hooks/use-upload-user-income-iability-document';
import { useEffect, useMemo, useState } from 'react';
import { useForm } from 'react-hook-form';
import { useTranslation } from 'react-i18next';
import { toast } from 'react-toastify';
import { useMount } from 'react-use';
import { formatCreditLimitRecalculationToIncomeVerificationPageDataFormat } from 'services';
import {
  defaultUploadMaxSize,
  getOccupationCategoriesOptions,
} from 'services/occupation-service';
import { processGqlFormValidationErrors } from 'utils';
import * as z from 'zod';

const OccupationFormSchema = z.object({
  [FormFieldNames.occupationCategory]: z.nativeEnum(OccupationCategory),
});

export type OccupationFormType = z.infer<typeof OccupationFormSchema>;
export const useIncomeVerificationPageLogic = () => {
  const { ruderStackEvents } = useRudderStack();
  const [isUploading, setIsUploading] = useState(false);
  const { t } = useTranslation(LocizeNamespaces.incomeVerification);
  const { t: tr } = useTranslation(LocizeNamespaces.errors);

  const [documentIdToDelete, setDocumentIdToDelete] = useState<number | null>(
    null,
  );

  const {
    user,
    quietUserRefetch,
    getPageUrlAndNavigate,
    pageUrlAndNavigationProcessing,
  } = useRootContext();

  const { uploadUserIncomeLiabilitiesGroupDocument } =
    useUploadUserIncomeLiabilitiesGroupDocument();

  const {
    userIncomeLiabilityDocumentsLoading,
    refetchUserIncomeLiabilityDocuments,
    getIncomeLiabilityDocuments,
    userIncomeLiabilityDocuments,
  } = useUserIncomeLiabilityDocuments();

  const {
    deleteIncomesLiabilitiesDocument,
    deleteIncomesLiabilitiesDocumentLoading,
  } = useDeleteIncomesLiabilitiesDocument();

  const { occupationCategories, occupationCategoriesLoading } =
    useGetOccupationCategoriesDefault({});
  const { updateUserInfoExtra } = useUpdateUserInfoExtra();

  const {
    getCreditLimitRecalculation,
    creditLimitRecalculation,
    creditLimitRecalculationLoading,
  } = useGetCreditLimitRecalculation();

  const {
    ultimateBeneficialOwner,
    futureReducedEarnings,
    planningNewDebts,
    employmentDate,
    numberOfDependents,
    monthlyLivingExpenses,
    expenditureMonthly,
    netIncomeMonthly,
    occupationCategory,
    applicationUserInfoId,
    overdueDebt,
  } = useMemo(
    () =>
      formatCreditLimitRecalculationToIncomeVerificationPageDataFormat({
        ...creditLimitRecalculation,
      }),
    [creditLimitRecalculation],
  );

  const creditAccountId = user?.credit_accounts?.length
    ? user.credit_accounts[0]?.id
    : null;

  const userId = user?.id ?? null;

  const incomeVerificationPageDataLoading =
    creditLimitRecalculationLoading || occupationCategoriesLoading;

  const form = useForm<OccupationFormType>({
    resolver: zodResolver(OccupationFormSchema),
    mode: 'onChange',
    defaultValues: {
      [FormFieldNames.occupationCategory]: undefined,
    },
  });

  useMount(() => {
    getCreditLimitRecalculation();
  });

  useEffect(() => {
    if (occupationCategory && occupationCategories) {
      form.setValue(FormFieldNames.occupationCategory, occupationCategory);
    }
  }, [occupationCategory, form, occupationCategories]);

  const occupationCategoryOptions = getOccupationCategoriesOptions({
    t,
    occupationCategories,
  });

  useMount(() => {
    if (!creditAccountId) {
      throw new Error('Credit account not found during  document request');
    }

    if (!userId) {
      throw new Error('User not found during document request');
    }

    getIncomeLiabilityDocuments({
      credit_account_id: creditAccountId,
      user_id: userId,
    });
  });

  const onOccupationFormSubmit = async ({
    occupation_category,
  }: OccupationFormType) => {
    try {
      await updateUserInfoExtra({
        application_user_info_id: applicationUserInfoId ?? 0,
        number_of_dependents: numberOfDependents,
        expenditure_monthly: expenditureMonthly,
        monthly_living_expenses: monthlyLivingExpenses,
        net_income_monthly: netIncomeMonthly,
        planning_new_debts: planningNewDebts,
        employment_date: employmentDate,
        future_reduced_earnings: futureReducedEarnings,
        ultimate_beneficial_owner: ultimateBeneficialOwner,
        occupation_category,
        overdue_debt: overdueDebt,
        extra_income: 0,
      });

      await quietUserRefetch();

      await getPageUrlAndNavigate(true);
    } catch (error) {
      processGqlFormValidationErrors({
        error,
        setFormError: form.setError,
      });
    }
  };

  const onDocumentsUploaded = async (files: File[]) => {
    if (!creditAccountId) {
      throw new Error('Credit account not found during document upload');
    }

    if (!userId) {
      throw new Error('User not found during document upload');
    }
    const maxFileSize = defaultUploadMaxSize * 1024 * 1024;
    try {
      setIsUploading(true);

      const oversizedFile = files.find((file) => file.size > maxFileSize);

      if (oversizedFile) {
        // request will fail
        toast.error(
          tr(LOCIZE_ERRORS_TRANSLATION_KEYS.uploadFileMaxSize, {
            max_file_size: defaultUploadMaxSize,
          }) as string,
        );
        return;
      }

      await uploadUserIncomeLiabilitiesGroupDocument({
        user_id: userId,
        credit_account_id: creditAccountId,
        documents: files,
      });

      await refetchUserIncomeLiabilityDocuments();
    } finally {
      setIsUploading(false);
    }
  };

  const onDocumentRemoved = async (id: number) => {
    try {
      setDocumentIdToDelete(id);

      const { data } = await deleteIncomesLiabilitiesDocument({
        user_income_liability_document_id: id,
      });

      if (data?.success) {
        await refetchUserIncomeLiabilityDocuments();
        setDocumentIdToDelete(null);
      }
    } catch {
      toast.error(tr(LOCIZE_ERRORS_TRANSLATION_KEYS.generalError) as string);
    } finally {
      setDocumentIdToDelete(null);
    }
  };

  return useMemo(
    () => ({
      onOccupationFormSubmit,
      onDocumentsUploaded,
      onDocumentRemoved,
      occupationCategoryOptions,
      form,
      userIncomeLiabilityDocuments,
      userIncomeLiabilityDocumentsLoading,
      deleteIncomesLiabilitiesDocumentLoading,
      documentIdToDelete,
      isUploading,
      pageUrlAndNavigationProcessing,
      getPageUrlAndNavigate,
      incomeVerificationPageDataLoading,
    }),
    [
      onOccupationFormSubmit,
      occupationCategoryOptions,
      onDocumentsUploaded,
      onDocumentRemoved,
      form,
      userIncomeLiabilityDocuments,
      userIncomeLiabilityDocumentsLoading,
      deleteIncomesLiabilitiesDocumentLoading,
      documentIdToDelete,
      isUploading,
      pageUrlAndNavigationProcessing,
      getPageUrlAndNavigate,
      incomeVerificationPageDataLoading,
    ],
  );
};
